package com.nacos.service.processor.impl;

import com.nacos.entity.context.TaskContext;
import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.model.SoundView.SoundViewApiUtil;
import com.nacos.result.Result;
import com.nacos.service.processor.UniversalTaskProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一视频翻译任务处理器
 * 
 * 实现UniversalTaskProcessor接口，处理视频翻译任务的具体业务逻辑。
 * 主要功能包括：
 * 1. 参数解析和验证
 * 2. 羚羊平台API调用
 * 3. 异步回调处理
 * 4. 任务状态管理
 * 5. 错误处理和重试
 * 
 * 设计原则：
 * 1. 统一接口：实现UniversalTaskProcessor统一接口
 * 2. 异步处理：支持异步任务提交和回调处理
 * 3. 状态管理：完整的任务状态生命周期管理
 * 4. 错误处理：完善的异常处理和错误恢复机制
 * 5. 监控统计：详细的处理统计和性能监控
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务处理器架构
 */
@Component("videoTranslateProcessor")
@Slf4j
public class UniversalVideoTranslateProcessor implements UniversalTaskProcessor<VideoTranslateRequestDTO> {

    @Autowired
    private VideoTranslateTaskMapper videoTranslateTaskMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理器统计信息
     */
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    @Override
    public TaskProcessResult process(TaskContext<VideoTranslateRequestDTO> context) {
        String methodName = "process";
        String taskId = context.getTaskId();
        String userId = context.getUserId();
        
        log.info("[{}] 开始处理视频翻译任务: taskId={}, userId={}", methodName, taskId, userId);
        
        try {
            // 更新统计
            incrementStat("totalProcessed");
            
            // 1. 参数验证
            VideoTranslateRequestDTO requestDTO = context.getBusinessParams();
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败: taskId={}, userId={}", methodName, taskId, userId);
                incrementStat("validationFailures");
                return TaskProcessResult.failure("参数验证失败");
            }

            // 2. 查询任务记录
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (taskPO == null) {
                log.error("[{}] 任务记录不存在: taskId={}", methodName, taskId);
                incrementStat("taskNotFound");
                return TaskProcessResult.failure("任务记录不存在");
            }

            // 3. 检查任务状态
            if (taskPO.isFinalStatus()) {
                log.warn("[{}] 任务已处于最终状态: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                incrementStat("alreadyFinalStatus");
                return TaskProcessResult.success("任务已完成", null);
            }

            // 4. 调用羚羊平台API提交任务
            Result<Map<String, Object>> apiResult = submitToLingyang(taskPO, requestDTO);
            
            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] 羚羊平台API调用失败: taskId={}, error={}", methodName, taskId, apiResult.getMessage());
                incrementStat("apiCallFailures");
                return TaskProcessResult.retryableFailure("羚羊平台API调用失败：" + apiResult.getMessage(), 300);
            }

            // 5. 解析API响应
            Map<String, Object> apiData = apiResult.getData();
            String lingyangTaskId = (String) apiData.get("lingyangTaskId");
            
            if (!StringUtils.hasText(lingyangTaskId)) {
                log.error("[{}] 羚羊平台返回的任务ID为空: taskId={}", methodName, taskId);
                incrementStat("invalidApiResponse");
                return TaskProcessResult.retryableFailure("羚羊平台返回的任务ID为空", 300);
            }

            // 6. 更新任务状态为处理中
            updateTaskStatus(taskPO, UnifiedTaskStatusEnum.PROGRESS.getCode(), lingyangTaskId, null);

            // 7. 构建处理结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("lingyangTaskId", lingyangTaskId);
            resultData.put("status", "processing");
            resultData.put("submitTime", System.currentTimeMillis());

            incrementStat("successfulSubmissions");
            log.info("[{}] 视频翻译任务提交成功: taskId={}, lingyangTaskId={}", methodName, taskId, lingyangTaskId);
            
            return TaskProcessResult.processing(lingyangTaskId);

        } catch (Exception e) {
            log.error("[{}] 处理视频翻译任务异常: taskId={}, userId={}", methodName, taskId, userId, e);
            incrementStat("processingErrors");
            return TaskProcessResult.retryableFailure("处理任务异常：" + e.getMessage(), 300);
        }
    }

    @Override
    public void handleCallback(String taskId, CallbackData callbackData) {
        String methodName = "handleCallback";
        log.info("[{}] 处理视频翻译回调: taskId={}, externalJobId={}, success={}", 
                methodName, taskId, callbackData.getExternalJobId(), callbackData.getSuccess());
        
        try {
            // 更新统计
            incrementStat("totalCallbacks");
            
            // 1. 查询任务记录
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (taskPO == null) {
                log.error("[{}] 回调处理失败，任务记录不存在: taskId={}", methodName, taskId);
                incrementStat("callbackTaskNotFound");
                return;
            }

            // 2. 检查任务状态
            if (taskPO.isFinalStatus()) {
                log.warn("[{}] 任务已处于最终状态，忽略回调: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                incrementStat("callbackIgnored");
                return;
            }

            // 3. 根据回调结果更新任务状态
            if (callbackData.getSuccess()) {
                // 成功回调
                handleSuccessCallback(taskPO, callbackData);
                incrementStat("successfulCallbacks");
            } else {
                // 失败回调
                handleFailureCallback(taskPO, callbackData);
                incrementStat("failedCallbacks");
            }

            log.info("[{}] 视频翻译回调处理完成: taskId={}, externalJobId={}", 
                    methodName, taskId, callbackData.getExternalJobId());

        } catch (Exception e) {
            log.error("[{}] 处理视频翻译回调异常: taskId={}, externalJobId={}", 
                    methodName, taskId, callbackData.getExternalJobId(), e);
            incrementStat("callbackErrors");
        }
    }

    @Override
    public TaskType getSupportedTaskType() {
        return TaskType.VIDEO_TRANSLATE;
    }

    @Override
    public boolean isHealthy() {
        try {
            // 检查羚羊平台API配置
            return SoundViewApiUtil.isConfigValid();
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return false;
        }
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public String getDescription() {
        return "统一视频翻译任务处理器 - 支持羚羊平台API";
    }

    @Override
    public CallbackData queryExternalStatus(String externalJobId) {
        String methodName = "queryExternalStatus";
        try {
            log.debug("[{}] 查询外部任务状态: externalJobId={}", methodName, externalJobId);
            
            // 调用羚羊平台状态查询API
            Result<Map<String, Object>> result = SoundViewApiUtil.getTaskStatus(externalJobId);
            
            if (!result.isSuccess() || result.getData() == null) {
                log.warn("[{}] 查询外部任务状态失败: externalJobId={}, error={}", 
                        methodName, externalJobId, result.getMessage());
                return null;
            }

            // 转换为CallbackData
            Map<String, Object> statusData = result.getData();
            return convertToCallbackData(statusData);

        } catch (Exception e) {
            log.error("[{}] 查询外部任务状态异常: externalJobId={}", methodName, externalJobId, e);
            return null;
        }
    }

    @Override
    public boolean cancelExternalTask(String externalJobId) {
        String methodName = "cancelExternalTask";
        try {
            log.info("[{}] 取消外部任务: externalJobId={}", methodName, externalJobId);
            
            // 羚羊平台暂不支持任务取消，返回false
            log.warn("[{}] 羚羊平台暂不支持任务取消: externalJobId={}", methodName, externalJobId);
            return false;

        } catch (Exception e) {
            log.error("[{}] 取消外部任务异常: externalJobId={}", methodName, externalJobId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("supportedTaskType", getSupportedTaskType().getCode());
        config.put("description", getDescription());
        config.put("version", getVersion());
        config.put("healthy", isHealthy());
        config.put("apiConfigValid", SoundViewApiUtil.isConfigValid());
        config.put("startupTime", startupTime);
        return config;
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>(statistics);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        
        // 计算成功率
        Long totalProcessed = (Long) statistics.getOrDefault("totalProcessed", 0L);
        Long successfulSubmissions = (Long) statistics.getOrDefault("successfulSubmissions", 0L);
        if (totalProcessed > 0) {
            double successRate = (double) successfulSubmissions / totalProcessed * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "N/A");
        }
        
        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 验证请求参数
     */
    private boolean isValidRequest(VideoTranslateRequestDTO requestDTO, String userId) {
        if (requestDTO == null) {
            return false;
        }
        
        if (!StringUtils.hasText(userId)) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getVideoUrl())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getSourceLanguage())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getTargetLanguage())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getVoiceId())) {
            return false;
        }
        
        // 检查源语言和目标语言是否相同
        if (requestDTO.getSourceLanguage().equals(requestDTO.getTargetLanguage())) {
            return false;
        }
        
        return true;
    }

    /**
     * 提交任务到羚羊平台
     */
    private Result<Map<String, Object>> submitToLingyang(VideoTranslateTaskPO taskPO, VideoTranslateRequestDTO requestDTO) {
        try {
            return SoundViewApiUtil.submitVideoTranslation(
                    taskPO.getSourceVideoUrl(),
                    requestDTO.getSourceLanguage(),
                    requestDTO.getTargetLanguage(),
                    requestDTO.getVoiceId(),
                    taskPO.getTaskName(),
                    taskPO.getUserId()
            );
        } catch (Exception e) {
            log.error("调用羚羊平台API异常", e);
            return Result.ERROR("调用羚羊平台API异常：" + e.getMessage());
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(VideoTranslateTaskPO taskPO, Integer status, String externalJobId, String errorMessage) {
        try {
            taskPO.setStatus(status);
            // VideoTranslateTaskPO没有externalJobId字段，使用resultJson存储
            if (StringUtils.hasText(externalJobId)) {
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("externalJobId", externalJobId);
                resultData.put("lingyangTaskId", externalJobId);
                try {
                    taskPO.setResultJson(objectMapper.writeValueAsString(resultData));
                } catch (Exception e) {
                    log.warn("序列化结果数据失败", e);
                }
            }
            if (StringUtils.hasText(errorMessage)) {
                taskPO.setErrorMsg(errorMessage);
            }
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}", taskPO.getTaskId(), status, e);
        }
    }

    /**
     * 处理成功回调
     */
    private void handleSuccessCallback(VideoTranslateTaskPO taskPO, CallbackData callbackData) {
        // 更新任务状态为成功
        taskPO.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode());
        taskPO.setTranslatedVideoUrl(callbackData.getResultUrl());
        // VideoTranslateTaskPO没有progress字段，使用resultJson存储进度信息
        try {
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("progress", 100);
            resultData.put("resultUrl", callbackData.getResultUrl());
            resultData.put("completedTime", System.currentTimeMillis());
            taskPO.setResultJson(objectMapper.writeValueAsString(resultData));
        } catch (Exception e) {
            log.warn("序列化成功结果数据失败", e);
        }
        taskPO.setUpdateTime(LocalDateTime.now());

        videoTranslateTaskMapper.updateById(taskPO);

        log.info("视频翻译任务成功完成: taskId={}, resultUrl={}",
                taskPO.getTaskId(), callbackData.getResultUrl());
    }

    /**
     * 处理失败回调
     */
    private void handleFailureCallback(VideoTranslateTaskPO taskPO, CallbackData callbackData) {
        // 更新任务状态为失败
        taskPO.setStatus(UnifiedTaskStatusEnum.FAILED.getCode());
        taskPO.setErrorMsg(callbackData.getErrorMessage());
        taskPO.setUpdateTime(LocalDateTime.now());

        videoTranslateTaskMapper.updateById(taskPO);

        log.warn("视频翻译任务失败: taskId={}, error={}",
                taskPO.getTaskId(), callbackData.getErrorMessage());
    }

    /**
     * 转换状态数据为回调数据
     */
    private CallbackData convertToCallbackData(Map<String, Object> statusData) {
        CallbackData callbackData = new CallbackData();
        
        String status = (String) statusData.get("status");
        callbackData.setStatus(status);
        callbackData.setSuccess("completed".equals(status));
        callbackData.setResultUrl((String) statusData.get("resultVideoUrl"));
        callbackData.setErrorMessage((String) statusData.get("errorMessage"));
        callbackData.setProgress((Integer) statusData.get("progress"));
        
        return callbackData;
    }

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        statistics.merge(key, 1L, (oldValue, newValue) -> 
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }
}
