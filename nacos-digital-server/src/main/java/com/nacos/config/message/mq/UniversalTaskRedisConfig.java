package com.nacos.config.message.mq;

import com.nacos.entity.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * 统一任务系统Redis消息队列配置
 * 
 * 负责配置Redis消息监听器容器，支持多队列并行处理：
 * 1. TOPIC_VIDEO_TRANSLATE - 视频翻译任务队列
 * 2. TOPIC_AUDIO_GENERATE - 音频生成任务队列  
 * 3. TOPIC_VIDEO_EDIT - 视频编辑任务队列
 * 
 * 设计原则：
 * 1. 队列隔离：不同任务类型使用独立队列，避免相互影响
 * 2. 并行处理：支持多队列同时处理，提高系统吞吐量
 * 3. 统一监听：使用统一的消息监听器处理所有队列
 * 4. 可扩展性：新增任务类型时只需添加对应队列配置
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务消息队列架构
 */
@Configuration
@Slf4j
public class UniversalTaskRedisConfig {

    @Autowired
    private UniversalTaskMessageListener universalTaskMessageListener;

    /**
     * Redis消息监听器容器
     * 
     * 配置所有任务类型的Redis队列监听器
     * 
     * @param connectionFactory Redis连接工厂
     * @param listenerAdapter   消息监听器适配器
     * @return Redis消息监听器容器
     */
    @Bean("universalTaskRedisContainer")
    public RedisMessageListenerContainer universalTaskRedisContainer(
            RedisConnectionFactory connectionFactory, 
            MessageListenerAdapter universalTaskListenerAdapter) {
        
        log.info("Initializing Universal Task Redis Message Listener Container");
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 配置视频翻译任务队列监听器
        container.addMessageListener(universalTaskListenerAdapter, 
                new PatternTopic(TaskType.VIDEO_TRANSLATE.getRedisTopic()));
        log.info("Added listener for VIDEO_TRANSLATE queue: {}", TaskType.VIDEO_TRANSLATE.getRedisTopic());
        
        // 配置音频生成任务队列监听器
        container.addMessageListener(universalTaskListenerAdapter, 
                new PatternTopic(TaskType.AUDIO_GENERATE.getRedisTopic()));
        log.info("Added listener for AUDIO_GENERATE queue: {}", TaskType.AUDIO_GENERATE.getRedisTopic());
        
        // 配置视频编辑任务队列监听器
        container.addMessageListener(universalTaskListenerAdapter, 
                new PatternTopic(TaskType.VIDEO_EDIT.getRedisTopic()));
        log.info("Added listener for VIDEO_EDIT queue: {}", TaskType.VIDEO_EDIT.getRedisTopic());
        
        log.info("Universal Task Redis Message Listener Container initialized successfully");
        return container;
    }

    /**
     * 统一任务消息监听器适配器
     * 
     * 将Redis消息适配到UniversalTaskMessageListener的onMessage方法
     * 
     * @param universalTaskMessageListener 统一任务消息监听器
     * @return 消息监听器适配器
     */
    @Bean("universalTaskListenerAdapter")
    public MessageListenerAdapter universalTaskListenerAdapter(
            UniversalTaskMessageListener universalTaskMessageListener) {
        
        log.info("Creating Universal Task Message Listener Adapter");
        
        // 指定监听器对象和处理方法名
        MessageListenerAdapter adapter = new MessageListenerAdapter(
                universalTaskMessageListener, "onMessage");
        
        log.info("Universal Task Message Listener Adapter created successfully");
        return adapter;
    }
}
